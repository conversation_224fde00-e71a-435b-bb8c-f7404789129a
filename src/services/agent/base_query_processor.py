"""
统一的查询处理器基类
提取runner.py的核心功能，为API和飞书处理提供统一的基础架构
"""
import asyncio
import json
import os
import queue
import concurrent.futures
import weakref
import time
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

from agents import Runner, RunConfig, ModelSettings, RunResultStreaming
from src.models.user_info_class import UserInfo
from src.services.agent.bots.coordinator_bot import CoordinatorBot
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.services.agent.utils.formatter import format_event_message
from src.services.agent.utils.model_provider import get_default_model
from src.services.chatbot.history_service import (
    get_conversation_history_as_input_list,
    create_streaming_assistant_message_record,
    update_streaming_assistant_message_content
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.image_utils import process_images_for_ai

QUEUE_TIMEOUT = int(os.getenv("QUEUE_TIMEOUT", 1800))
WORKER_POOL_SIZE = int(os.getenv("WORKER_POOL_SIZE", 5))
THREAD_POOL_SIZE = int(os.getenv("THREAD_POOL_SIZE", 20))
MAX_LOG_LENGTH = int(os.getenv("MAX_LOG_LENGTH", 1024*40))
STREAMING_UPDATE_THRESHOLD = int(os.getenv("STREAMING_UPDATE_THRESHOLD", 15))  # 流式更新阈值（字符数）- 降低到15字符确保更频繁的持久化


class BackgroundTaskManager:
    """后台任务管理器 - 处理中断后的任务继续执行"""

    def __init__(self, max_workers=THREAD_POOL_SIZE):
        self.task_queue = queue.Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="background_worker"
        )
        self.active_tasks = weakref.WeakSet()
        self._start_workers()

    def _start_workers(self):
        """启动后台工作线程"""
        for _ in range(WORKER_POOL_SIZE):  # 启动3个工作线程
            future = self.executor.submit(self._worker)
            self.active_tasks.add(future)

    def _worker(self):
        """后台工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=30)  # 30秒超时
                if task is None:
                    break

                # 处理任务
                self._process_task(task)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Background worker error: {e}")
            finally:
                self.task_queue.task_done()

    def _process_task(self, task):
        """处理后台任务"""
        try:
            processor = task.get("processor")
            request = task.get("request")

            if processor and request:
                # 创建消息队列并执行任务
                message_queue = queue.Queue()
                async_worker = processor.create_async_worker(request, message_queue)

                # 执行任务并消费结果
                async_worker()

                logger.info(f"后台任务完成 (Convo ID: {request.conversation_id})")

        except Exception as e:
            logger.error(f"Background task processing error: {e}")

    def submit_task(self, task):
        """提交后台任务"""
        self.task_queue.put(task)

    def shutdown(self):
        """优雅关闭后台任务管理器"""
        # 停止工作线程
        for _ in range(WORKER_POOL_SIZE):
            self.task_queue.put(None)

        # 关闭线程池
        self.executor.shutdown(wait=True)


# 全局线程池
THREAD_POOL = concurrent.futures.ThreadPoolExecutor(
    max_workers=THREAD_POOL_SIZE,
    thread_name_prefix="agent_worker"
)

# 全局后台任务管理器
BACKGROUND_MANAGER = BackgroundTaskManager()


@dataclass
class QueryRequest:
    """查询请求领域模型"""
    user_query: str
    user_info: Dict[str, Any]
    access_token: Optional[str] = None
    conversation_id: Optional[str] = None
    images: Optional[List[str]] = None
    agent_model_overrides: Optional[Dict[str, Dict[str, str]]] = None  # agent模型覆盖配置
    agent: Optional[str] = None  # 指定要使用的特定agent名称


@dataclass
class QueryResult:
    """查询结果领域模型"""
    response: str
    logs: str
    timestamp: int
    final_input_list: Optional[List[Dict]] = None
    bot_instance: Optional[CoordinatorBot] = None
    used_agents: Optional[List[str]] = None
    interrupted: bool = False


@dataclass
class StreamEvent:
    """流事件领域模型"""
    event_type: str
    content: str
    data: Optional[Any] = None


class BaseQueryProcessor(ABC):
    """统一的查询处理器基类"""

    def __init__(self, thread_pool: concurrent.futures.ThreadPoolExecutor):
        self.thread_pool = thread_pool
        # 缓存可用的agent列表，避免重复读取配置文件
        self._available_agents = None

    def get_available_agents(self) -> List[str]:
        """获取可用的agent列表，失败时返回默认列表"""
        if self._available_agents is None:
            # 默认的agent列表，作为fallback
            default_agents = ['sales_order_analytics', 'sales_kpi_analytics', 'warehouse_and_fulfillment', 'general_chat_bot']

            try:
                from src.utils.resource_manager import load_resource
                import yaml

                coordinator_config_content = load_resource("data_fetcher_bot_config", "coordinator_bot.yml")
                coordinator_config = yaml.safe_load(coordinator_config_content) if coordinator_config_content else {}
                agent_tools = coordinator_config.get("agent_tools", [])
                self._available_agents = [tool.get("name") for tool in agent_tools if tool.get("name")] or default_agents
                logger.info(f"从coordinator配置中获取到可用agent列表: {self._available_agents}")
            except Exception as e:
                logger.warning(f"获取可用agent列表失败，使用默认列表: {e}")
                self._available_agents = default_agents

        return self._available_agents

    def validate_agent_name(self, agent_name: str) -> bool:
        """验证agent名称是否有效"""
        return bool(agent_name and agent_name in self.get_available_agents())

    def create_direct_agent_bot(self, agent_name: str, user_info: Dict[str, Any]) -> Optional[DataFetcherBot]:
        """直接创建指定的agent bot实例，失败时返回None"""
        if not self.validate_agent_name(agent_name):
            logger.warning(f"无效的agent名称: {agent_name}，可用的agent: {self.get_available_agents()}")
            return None

        try:
            config_file = f"{agent_name}.yml"
            bot = DataFetcherBot(user_info, config_file)
            logger.info(f"成功创建直接agent bot: {agent_name}")
            return bot
        except Exception as e:
            logger.warning(f"创建agent bot失败 {agent_name}: {e}")
            return None

    def create_user_info_object(self, user_info: dict, access_token: str = None, conversation_id: str = None) -> UserInfo:
        """创建UserInfo对象"""
        user_name = user_info.get("name")
        email = get_valid_user_email(user_info)
        union_id = user_info.get("union_id")
        summerfarm_api_token = user_info.get("summerfarm_api_token")
        location = user_info.get("location")  # 获取位置信息

        return UserInfo(
            user_name=user_name,
            email=email,
            access_token=access_token,
            union_id=union_id,
            summerfarm_api_token=summerfarm_api_token,
            open_id=user_info.get("open_id", ""),
            conversation_id=conversation_id,
            location=location  # 传递位置信息
        )
    
    def get_conversation_history(self, user_info: dict, conversation_id: str) -> List[Dict]:
        """获取对话历史，失败时返回空列表"""
        if not conversation_id:
            return []

        try:
            user_name = user_info.get("name")
            email = get_valid_user_email(user_info)
            history = get_conversation_history_as_input_list(user_name, email, conversation_id)
            logger.info(f"获取到 {len(history)} 条历史消息")
            return history
        except Exception as e:
            logger.warning(f"获取对话历史失败: {e}")
            return []
    
    def build_messages(self, user_query: str, images: List[str] = None, history: List[Dict] = None) -> List[Dict]:
        """构建消息列表，支持多模态，失败时回退到纯文本"""
        history = history or []

        # 处理图片（如果有）
        if images:
            try:
                logger.info(f"开始处理 {len(images)} 张图片")
                processed_images = process_images_for_ai(images)

                if processed_images:
                    content_parts = [{"type": "input_text", "text": user_query}]
                    content_parts.extend([
                        {"type": "input_image", "image_url": img, "detail": "auto"}
                        for img in processed_images
                    ])
                    user_message = {"role": "user", "content": content_parts}
                    logger.info(f"用户消息包含 {len(processed_images)} 张已处理的图片")
                else:
                    raise ValueError("所有图片处理失败")
            except Exception as e:
                logger.warning(f"图片处理失败，回退到纯文本: {e}")
                user_message = {"role": "user", "content": user_query}
        else:
            user_message = {"role": "user", "content": user_query}

        return history + [user_message]

    def _get_enhanced_logs(self, logs: str, bot_instance: CoordinatorBot = None) -> str:
        """获取增强的日志，包含CoordinatorBot的执行日志"""
        enhanced_logs = logs or ""

        if bot_instance and hasattr(bot_instance, 'log_items') and bot_instance.log_items:
            coordinator_logs = "\n".join(str(item) for item in bot_instance.log_items if item)
            if coordinator_logs.strip():
                separator = "\n\n=== Coordinator执行日志 ===\n" if enhanced_logs else ""
                enhanced_logs += separator + coordinator_logs
                logger.info(f"已添加Coordinator执行日志，总长度: {len(enhanced_logs)}")

        return enhanced_logs

    async def process_stream_events(self, result: RunResultStreaming, message_queue: queue.Queue, streaming_message_id: int = None) -> tuple:
        """统一的流事件处理逻辑，支持真正的流式数据库更新

        Args:
            result: AI流式结果对象
            message_queue: 消息队列
            streaming_message_id: 预创建的流式消息ID

        Returns:
            tuple: (collected_logs, tool_calls_detected, agent_execution_logs, accumulated_content, streaming_message_id)
        """
        collected_logs = ""
        tool_calls_detected = False
        agent_execution_logs = []
        accumulated_content = ""
        last_update_length = 0
        update_threshold = STREAMING_UPDATE_THRESHOLD

        try:
            async for event in result.stream_events():
                message = format_event_message(event)
                if message:
                    msg_type = message.get("type")
                    content = message.get("content", "")

                    # 收集所有相关日志
                    if msg_type in ["log", "handoff_log", "tool_output", "tool_call_log"] and content:
                        collected_logs += content + "\n"

                    # 检测工具调用日志（Agent as Tool架构的关键指标）
                    if msg_type == "tool_call_log" and content:
                        tool_calls_detected = True
                        logger.info(f"检测到工具调用: {content}")

                    # 处理 handoff_log，提取 agent 名称
                    if msg_type == "handoff_log" and content:
                        tool_calls_detected = True
                        agent_log = f"🔄 CoordinatorBot调用专业工具:{content}"
                        agent_execution_logs.append(agent_log)
                        logger.info(agent_log)

                    # 收集AI响应内容并进行实时流式数据库更新
                    if msg_type in ["data", "raw_response_event"] and content:
                        accumulated_content += content

                        # 实现流式数据库更新 - 使用预创建的消息ID
                        if streaming_message_id and len(accumulated_content) - last_update_length >= update_threshold:
                            try:
                                self.update_streaming_assistant_content_with_logs(
                                    streaming_message_id,
                                    accumulated_content,
                                    collected_logs,
                                    is_final=False
                                )
                                last_update_length = len(accumulated_content)
                                logger.debug(f"流式更新数据库 (ID: {streaming_message_id}, 内容长度: {len(accumulated_content)}, 日志长度: {len(collected_logs)})")
                            except Exception as e:
                                logger.warning(f"流式数据库更新失败: {e}")

                    # 实时发送消息到前端
                    message_queue.put(message)

        except Exception as e:
            logger.exception(f"流事件处理出错: {e}")
            raise

        return collected_logs, tool_calls_detected, agent_execution_logs, accumulated_content, streaming_message_id
    
    @abstractmethod
    def handle_message_output(self, message: dict) -> str:
        """处理消息输出 - 子类需要实现具体的输出格式"""
        pass
    
    def pre_create_assistant_response(self, user_info: dict, conversation_id: str) -> Optional[int]:
        """在AI开始处理前预创建空的assistant回复记录

        Args:
            user_info: 用户信息字典
            conversation_id: 对话ID

        Returns:
            message_id: 创建的消息ID，如果失败返回None
        """
        try:
            user_name = user_info.get("name")
            email = get_valid_user_email(user_info)

            if conversation_id:
                # 使用当前时间戳创建记录
                timestamp = int(datetime.now().timestamp() * 1000)
                message_id = create_streaming_assistant_message_record(
                    username=user_name,
                    email=email,
                    conversation_id=conversation_id,
                    timestamp=timestamp
                )
                logger.info(f"预创建assistant消息记录 (ID: {message_id}, Convo ID: {conversation_id})")
                return message_id

        except Exception as e:
            logger.error(f"预创建assistant消息记录时出错: {e}")
            return None

    def update_streaming_assistant_content(self, message_id: int, content: str, is_final: bool = False) -> bool:
        """更新流式assistant消息的内容（简化版本）

        Args:
            message_id: 消息ID
            content: 更新的内容
            is_final: 是否为最终更新

        Returns:
            bool: 更新成功返回True，失败返回False
        """
        try:
            success = update_streaming_assistant_message_content(
                message_id=message_id,
                content=content,
                is_completed=is_final
            )
            if success:
                status = "完成" if is_final else "更新"
                logger.debug(f"流式assistant消息{status} (ID: {message_id})")
            return success
        except Exception as e:
            logger.error(f"更新流式assistant消息时出错: {e}")
            return False

    def update_streaming_assistant_content_with_logs(self, message_id: int, content: str, logs: str = None, is_final: bool = False) -> bool:
        """更新流式assistant消息的内容和日志

        Args:
            message_id: 消息ID
            content: 更新的内容
            logs: 更新的日志
            is_final: 是否为最终更新

        Returns:
            bool: 更新成功返回True，失败返回False
        """
        try:
            success = update_streaming_assistant_message_content(
                message_id=message_id,
                content=content,
                logs=logs,
                is_completed=is_final
            )
            if success:
                status = "完成" if is_final else "更新"
                logger.debug(f"流式assistant消息{status} (ID: {message_id}, 内容长度: {len(content)}, 日志长度: {len(logs) if logs else 0})")
            return success
        except Exception as e:
            logger.error(f"更新流式assistant消息时出错: {e}")
            return False
    
    def finalize_streaming_assistant_response(self, message_id: int, content: str, logs: str = None,
                                            final_input_list: List[Dict] = None, used_agents: List[str] = None,
                                            bot_instance: CoordinatorBot = None, time_spend: Optional[int] = None):
        """完成流式assistant回复的最终更新

        Args:
            message_id: 消息ID
            content: 最终内容
            logs: 执行日志
            final_input_list: 最终输入列表
            used_agents: 使用的agent列表
            bot_instance: CoordinatorBot实例
            time_spend: AI响应耗时(秒)
        """
        try:
            # 处理结构化消息
            output_as_input_json = None
            if final_input_list and isinstance(final_input_list, list) and len(final_input_list) > 0:
                last_message = final_input_list[-1]
                if isinstance(last_message, dict) and last_message.get("role") == "assistant":
                    try:
                        output_as_input_json = json.dumps(last_message, ensure_ascii=False, indent=2)
                    except Exception as e:
                        logger.error(f"序列化结构化消息失败: {e}")

            # 获取使用的agent信息
            used_agents_str = None
            if used_agents and len(used_agents) > 0:
                agent_strings = [str(agent) for agent in used_agents if agent]
                if agent_strings:
                    used_agents_str = ",".join(agent_strings)
                    if len(used_agents_str) > 100:
                        used_agents_str = used_agents_str[:97] + "..."
                        logger.warning(f"Agent字符串过长，已截断: {used_agents_str}")
                    logger.info(f"本次查询使用的agent: {used_agents_str}")

            # 获取增强的日志
            enhanced_logs = self._get_enhanced_logs(logs, bot_instance)
            if enhanced_logs:
                enhanced_logs = enhanced_logs[-MAX_LOG_LENGTH:]

            # 记录耗时信息
            if time_spend is not None:
                logger.info(f"AI响应耗时: {time_spend}秒")

            # 执行最终更新 - 简化参数处理，content已经是字符串
            update_streaming_assistant_message_content(
                message_id=message_id,
                content=content or "",
                logs=enhanced_logs,
                output_as_input=output_as_input_json,
                agent=used_agents_str,
                time_spend=time_spend,
                is_completed=True
            )

            logger.info(f"流式assistant消息最终完成 (ID: {message_id})")

        except Exception as e:
            logger.error(f"完成流式assistant消息时出错: {e}")



    async def execute_agent_stream(self, bot: CoordinatorBot|DataFetcherBot, messages: List[Dict],
                                 user_obj: UserInfo, message_queue: queue.Queue,
                                 streaming_message_id: int = None) -> tuple:
        """执行Agent流式处理的核心逻辑

        Args:
            bot: Bot实例（CoordinatorBot或DataFetcherBot）
            messages: 消息列表
            user_obj: 用户对象
            message_queue: 消息队列
            streaming_message_id: 预创建的流式消息ID

        Returns:
            tuple: (final_input_list, bot, used_agent_names, time_spend)
        """
        # 记录开始时间
        start_time = time.time()

        # 重置CoordinatorBot的日志追踪，开始新的查询
        if hasattr(bot, 'log_items'):
            bot.log_items.clear()
        agent = bot.create_agent(get_default_model())

        total_messages = bot.get_user_realtime_instruction_as_message_object() + messages

        logger.info("开始处理查询")

        # 启动流式推理
        result: RunResultStreaming = Runner.run_streamed(
            agent,
            input=total_messages,
            max_turns=20,
            run_config=RunConfig(
                model_settings=ModelSettings(temperature=0.1)
            ),
            context=user_obj,
        )

        try:
            # 使用预创建的消息ID进行流式处理
            stream_result = await self.process_stream_events(result, message_queue, streaming_message_id)

            # 处理返回值
            collected_logs, _, agent_execution_logs, accumulated_content, _ = stream_result

            # 从 agent_execution_logs 中提取 agent 名称
            used_agent_names = []
            for log in agent_execution_logs:
                # 从日志中提取agent名称，格式: "🔄 CoordinatorBot调用专业工具:agent_name"
                if "CoordinatorBot调用专业工具:" in log:
                    agent_name = log.split("CoordinatorBot调用专业工具:")[-1].strip()
                    if agent_name and agent_name not in used_agent_names:
                        used_agent_names.append(agent_name)

            # 如果是直接调用agent（非CoordinatorBot），则直接使用bot的agent名称
            if isinstance(bot, DataFetcherBot) and hasattr(bot, 'agent_name'):
                if bot.agent_name and bot.agent_name not in used_agent_names:
                    used_agent_names.append(bot.agent_name)
                    logger.info(f"直接调用agent: {bot.agent_name}")

        except Exception as e:
            logger.exception(f"流事件处理出错: {e}")
            if "Event loop is closed" in str(e):
                message_queue.put({"type": "error", "content": "事件循环已关闭"})
            else:
                message_queue.put({"type": "error", "content": f"处理查询时出错: {str(e)}"})

            # 计算耗时并返回错误结果
            end_time = time.time()
            time_spend = int(end_time - start_time)
            message_queue.put({"type": "bot_instance", "data": bot})
            message_queue.put({"type": "used_agents", "data": []})
            message_queue.put({"type": "time_spend", "data": time_spend})
            message_queue.put({"type": "final_result", "data": None})
            return None, bot, [], time_spend

        # 处理执行结果
        logger.info("查询处理完成")
        if agent_execution_logs:
            logger.info(f"调用了 {len(agent_execution_logs)} 个专业工具")

        final_input_list = result.to_input_list()

        # 计算总耗时
        end_time = time.time()
        time_spend = int(end_time - start_time)

        # 完成流式保存
        if streaming_message_id:
            try:
                # 获取最终响应内容 - 简化处理，content已经是字符串
                final_response = accumulated_content
                if final_input_list and len(final_input_list) > 0:
                    last_message = final_input_list[-1]
                    if isinstance(last_message, dict) and last_message.get("role") == "assistant":
                        content = last_message.get("content", "")
                        # content已经是字符串，直接使用
                        if content and len(content) > len(accumulated_content):
                            final_response = content
                            logger.info(f"使用从final_input_list的内容，长度: {len(content)}")
                        else:
                            logger.info(f"使用accumulated_content，长度: {len(accumulated_content)}")

                # 使用最终更新方法
                self.finalize_streaming_assistant_response(
                    streaming_message_id, final_response, collected_logs,
                    final_input_list, used_agent_names, bot, time_spend
                )
                logger.info(f"流式保存完成 (ID: {streaming_message_id})")

            except Exception as e:
                logger.error(f"完成流式保存时出错: {e}")

        # 将bot对象、使用的agent名称和耗时传递给外部
        message_queue.put({"type": "bot_instance", "data": bot})
        message_queue.put({"type": "used_agents", "data": used_agent_names})
        message_queue.put({"type": "time_spend", "data": time_spend})
        message_queue.put({"type": "final_result", "data": final_input_list})
        return final_input_list, bot, used_agent_names, time_spend

    def create_async_worker(self, request: QueryRequest, message_queue: queue.Queue):
        """创建异步工作函数"""
        def async_worker():
            loop = None
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # 运行协程
                loop.run_until_complete(self._process_stream_async(request, message_queue))

            except Exception as e:
                logger.exception(f"异步工作线程出错: {e}")
                message_queue.put({"type": "error", "content": str(e)})
                message_queue.put({"type": "final_result", "data": None})
            finally:
                # 确保事件循环被正确关闭
                if loop and not loop.is_closed():
                    try:
                        # 取消所有待处理的任务
                        pending = asyncio.all_tasks(loop)
                        for task in pending:
                            task.cancel()

                        # 等待任务取消完成
                        if pending:
                            loop.run_until_complete(
                                asyncio.gather(*pending, return_exceptions=True)
                            )

                        loop.close()
                        logger.info("Event loop closed properly")
                    except Exception as e:
                        logger.error(f"Error closing event loop: {e}")

                message_queue.put(None)  # 结束标记

        return async_worker

    async def _process_stream_async(self, request: QueryRequest, message_queue: queue.Queue):
        """处理流事件的协程 - 集成预插入机制"""
        streaming_message_id = None
        try:
            # 1. 预创建assistant消息记录（关键改进：在AI开始处理前立即创建）
            if request.conversation_id:
                streaming_message_id = self.pre_create_assistant_response(
                    request.user_info, request.conversation_id
                )
                if streaming_message_id:
                    logger.info(f"预创建assistant记录成功 (ID: {streaming_message_id}, Convo ID: {request.conversation_id})")
                else:
                    logger.warning(f"预创建assistant记录失败 (Convo ID: {request.conversation_id})")

            # 2. 获取历史
            history = self.get_conversation_history(request.user_info, request.conversation_id)

            # 3. 构建消息
            messages = self.build_messages(request.user_query, request.images, history)

            # 4. 创建用户对象
            user_obj = self.create_user_info_object(
                request.user_info, request.access_token, request.conversation_id
            )

            # 5. 应用agent模型覆盖配置（如果有的话）
            model_config_manager = None
            if request.agent_model_overrides:
                model_config_manager = self._apply_agent_model_overrides(request.agent_model_overrides)

            bot = None
            # 6. 创建Bot - 根据是否指定agent参数选择创建方式
            if request.agent:
                # 直接创建指定的agent bot
                logger.info(f"使用指定的agent: {request.agent}")
                try:
                    bot = self.create_direct_agent_bot(request.agent, request.user_info)
                    logger.info(f"成功创建直接agent bot: {request.agent}")
                except ValueError as e:
                    logger.exception(f"创建指定agent:{request.agent}失败: {e}")
                    pass
            if not bot:
                # 使用默认的coordinator调度
                logger.info("使用coordinator调度模式")
                bot = CoordinatorBot(request.user_info)

            # 7. 执行Agent流式处理，传递预创建的消息ID
            try:
                await self.execute_agent_stream(bot, messages, user_obj, message_queue, streaming_message_id)
            finally:
                # 清理模型配置覆盖
                if model_config_manager:
                    model_config_manager.cleanup()

        except Exception as e:
            logger.exception(f"处理流时出错: {e}")
            # 即使异常也要尝试传递bot实例（如果存在的话）
            try:
                if 'bot' in locals():
                    message_queue.put({"type": "bot_instance", "data": bot})
                if 'used_agent_names' in locals():
                    message_queue.put({"type": "used_agents", "data": used_agent_names})
            except Exception as inner_e:
                logger.warning(f"传递bot实例时出错: {inner_e}")
            message_queue.put({"type": "error", "content": str(e)})
            message_queue.put({"type": "final_result", "data": None})

    def _apply_agent_model_overrides(self, agent_model_overrides: Dict[str, Dict[str, str]]):
        """
        应用agent模型覆盖配置

        Args:
            agent_model_overrides: 格式为 {agent_name: {provider: str, model: str}}

        Returns:
            ModelConfigManager: 模型配置管理器实例，用于后续清理
        """
        try:
            # 导入ModelConfigManager
            from src.services.agent.utils.model_config_manager import ModelConfigManager

            # 过滤掉coordinator_bot的配置（禁止覆盖coordinator_bot的模型）
            filtered_overrides = {}
            for agent_name, config in agent_model_overrides.items():
                if agent_name == 'coordinator_bot':
                    logger.warning(f"禁止覆盖coordinator_bot的模型配置，已忽略: {agent_name}")
                    continue
                filtered_overrides[agent_name] = config

            if not filtered_overrides:
                logger.info("没有有效的agent模型覆盖配置")
                return None

            # 创建模型配置管理器并应用覆盖
            model_config_manager = ModelConfigManager()
            model_config_manager.apply_model_overrides(filtered_overrides)

            logger.info(f"成功应用agent模型覆盖配置: {filtered_overrides}")
            return model_config_manager

        except ImportError as e:
            logger.warning(f"无法导入ModelConfigManager，跳过模型覆盖: {e}")
            return None
        except Exception as e:
            logger.error(f"应用agent模型覆盖配置时出错: {e}")
            return None

    def handle_client_disconnect(self, request: QueryRequest):
        """处理客户端断开连接的情况"""
        try:
            # 提交后台任务继续执行
            BACKGROUND_MANAGER.submit_task({
                "processor": self,
                "request": request
            })
            logger.info(f"任务已提交到后台队列 (Convo ID: {request.conversation_id})")
        except Exception as e:
            logger.error(f"提交后台任务时出错: {e}")

    @staticmethod
    def shutdown_gracefully():
        """优雅关闭所有资源"""
        logger.info("开始优雅关闭BaseQueryProcessor资源...")

        try:
            # 关闭后台任务管理器
            BACKGROUND_MANAGER.shutdown()
            logger.info("后台任务管理器已关闭")
        except Exception as e:
            logger.error(f"关闭后台任务管理器时出错: {e}")

        logger.info("BaseQueryProcessor资源优雅关闭完成")


# 注册优雅关闭函数
import atexit
atexit.register(BaseQueryProcessor.shutdown_gracefully)
