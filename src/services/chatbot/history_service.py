"""
Chat history service module.

This module provides business logic for managing chatbot history.
"""

from typing import List, Dict, Any, Optional
import uuid
import json
import os

from src.utils.logger import logger
from src.repositories.chatbi.history import (
    save_message,
    load_history,
    load_conversation,
    delete_conversation,
    get_history_conversation_count,
    check_conversation_owner,
    get_user_latest_queries_from_db,
    get_other_users_latest_queries_from_db,
    create_streaming_assistant_message,
    update_streaming_assistant_message,
    get_streaming_assistant_message_id,
)
from src.repositories.chatbi.good_case import get_conversation_good_case_feedback, get_bulk_good_case_feedback
from src.repositories.chatbi.bad_case import get_conversation_bad_case_feedback, get_bulk_bad_case_feedback

MAX_HISTORY_MESSAGE_TO_INCLUDE=int(os.getenv("MAX_HISTORY_MESSAGE_TO_INCLUDE", 16))

def save_user_message(username: str, email: str, conversation_id: str, content: str,
                     timestamp: Optional[int] = None, images: Optional[List[str]] = None) -> bool:
    """
    Save a user message to the chatbot history.

    Args:
        username (str): The username of the user
        email (str): The email of the user
        conversation_id (str): The ID of the conversation
        content (str): The content of the message
        timestamp (int, optional): The timestamp of the message. Defaults to None.
        images (List[str], optional): List of image URLs. Defaults to None.

    Returns:
        bool: True if the message was saved successfully, False otherwise
    """
    logger.info(f"Saving user message for {username} ({email}) in conversation {conversation_id}")

    # 处理图片URL
    resource_url = None
    if images and len(images) > 0:
        # 将图片URL列表转换为逗号分隔的字符串
        resource_url = ','.join(images)
        logger.info(f"User message includes {len(images)} images")

    return save_message(username, email, conversation_id, 'user', content, timestamp, resource_url=resource_url)

def save_assistant_message(username: str, email: str, conversation_id: str, content: str,
                          timestamp: Optional[int] = None, logs: Optional[str] = None,
                          output_as_input: Optional[str] = None, agent: Optional[str] = None,
                          time_spend: Optional[int] = None) -> bool:
    """
    Save an assistant message to the chatbot history.

    Args:
        username (str): The username of the user
        email (str): The email of the user
        conversation_id (str): The ID of the conversation
        content (str): The content of the message
        timestamp (int, optional): The timestamp of the message. Defaults to None.
        logs (str, optional): Additional logs for the message. Defaults to None.
        output_as_input (str, optional): Structured output intended as input for the next turn (JSON format).
        agent (str, optional): The agent name(s) that processed this message. If None, will be extracted from logs.
        time_spend (int, optional): AI响应耗时(秒)，从发起Agent请求开始到AI响应完成. Defaults to None.

    Returns:
        bool: True if the message was saved successfully, False otherwise
    """
    logger.info(f"Saving assistant message for {username} ({email}) in conversation {conversation_id}")
    # Pass the agent and time_spend parameters to save_message function
    return save_message(username, email, conversation_id, 'assistant', content, timestamp, logs, output_as_input, agent, resource_url=None, time_spend=time_spend)

def get_user_conversations(username: str, email: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """
    Get a list of conversations for a user, including feedback information.

    Args:
        username (str): The username of the user
        email (str): The email of the user
        limit (int, optional): Maximum number of conversations to return. Defaults to 100.
        offset (int, optional): Number of conversations to skip. Defaults to 0.

    Returns:
        List[Dict[str, Any]]: A list of conversations with feedback information
    """
    logger.info(f"Getting conversations for {username} ({email})")

    # 获取基本的历史记录数据
    history_data = load_history(username, email, limit, offset)

    # 使用批量查询为所有对话添加反馈信息
    if history_data:
        conversation_ids = list(history_data.keys())
        logger.debug(f"Processing {len(conversation_ids)} conversations for feedback using batch queries")
        
        try:
            # 使用批量查询获取所有对话的好案例和坏案例反馈信息
            good_feedback_map = get_bulk_good_case_feedback(conversation_ids)
            bad_feedback_map = get_bulk_bad_case_feedback(conversation_ids)
            
            logger.debug(f"Got bulk good feedback for {len(good_feedback_map)} conversations")
            logger.debug(f"Got bulk bad feedback for {len(bad_feedback_map)} conversations")
            
            # 批量处理所有对话的反馈信息
            for conversation_id, messages in history_data.items():
                if messages and len(messages) > 0:
                    try:
                        good_feedback = good_feedback_map.get(conversation_id)
                        bad_feedback = bad_feedback_map.get(conversation_id)
                        
                        # 将反馈信息添加到第一条消息中（因为反馈是对话级别的）
                        # 需要将datetime对象转换为字符串，以便JSON序列化
                        if good_feedback and 'feedback_submitted_at' in good_feedback and good_feedback['feedback_submitted_at']:
                            good_feedback['feedback_submitted_at'] = good_feedback['feedback_submitted_at'].isoformat()
                        if bad_feedback and 'feedback_submitted_at' in bad_feedback and bad_feedback['feedback_submitted_at']:
                            bad_feedback['feedback_submitted_at'] = bad_feedback['feedback_submitted_at'].isoformat()

                        messages[0]['goodCaseFeedback'] = good_feedback
                        messages[0]['badCaseFeedback'] = bad_feedback
                    except Exception as e:
                        logger.error(f"Failed to process feedback for conversation {conversation_id}: {e}")
                        # 如果处理反馈失败，仍然返回消息，但没有反馈信息
        except Exception as e:
            logger.error(f"Failed to get bulk feedback: {e}")
            # 如果批量查询失败，回退到逐个查询（但不建议，因为性能问题）
            # 这里直接跳过反馈信息的添加，因为批量查询失败通常是数据库问题

    return history_data

def get_conversation_messages(conversation_id: str, username: str=None, email: str=None ) -> List[Dict[str, Any]]:
    """
    Get all messages in a conversation, including feedback information.

    Args:
        username (str): The username of the user
        email (str): The email of the user
        conversation_id (str): The ID of the conversation

    Returns:
        List[Dict[str, Any]]: A list of messages in the conversation with feedback information
    """
    logger.info(f"Getting messages for conversation {conversation_id} for {username} ({email})")

    # 获取基本的消息数据
    messages = load_conversation(conversation_id=conversation_id, username=username, email=email)

    if not messages:
        return messages

    # 获取反馈信息 - 即使是单个对话也使用批量查询统一接口
    try:
        conversation_ids = [conversation_id]
        good_feedback_map = get_bulk_good_case_feedback(conversation_ids)
        bad_feedback_map = get_bulk_bad_case_feedback(conversation_ids)
        
        good_feedback = good_feedback_map.get(conversation_id)
        bad_feedback = bad_feedback_map.get(conversation_id)

        # 将反馈信息添加到第一条消息中（因为反馈是对话级别的）
        if len(messages) > 0:
            # 需要将datetime对象转换为字符串，以便JSON序列化
            if good_feedback and 'feedback_submitted_at' in good_feedback and good_feedback['feedback_submitted_at']:
                good_feedback['feedback_submitted_at'] = good_feedback['feedback_submitted_at'].isoformat()
            if bad_feedback and 'feedback_submitted_at' in bad_feedback and bad_feedback['feedback_submitted_at']:
                bad_feedback['feedback_submitted_at'] = bad_feedback['feedback_submitted_at'].isoformat()

            messages[0]['goodCaseFeedback'] = good_feedback
            messages[0]['badCaseFeedback'] = bad_feedback
            logger.info(f"Added feedback info to conversation {conversation_id}: good={good_feedback is not None}, bad={bad_feedback is not None}")
    except Exception as e:
        logger.error(f"Failed to get feedback for conversation {conversation_id}: {e}")
        # 如果获取反馈失败，仍然返回消息，但没有反馈信息

    return messages

def delete_user_conversation(username: str, email: str, conversation_id: str) -> bool:
    """
    Delete a conversation.

    Args:
        username (str): The username of the user
        email (str): The email of the user
        conversation_id (str): The ID of the conversation

    Returns:
        bool: True if the conversation was deleted successfully, False otherwise
    """
    logger.info(f"Deleting conversation {conversation_id} for {username} ({email})")
    return delete_conversation(username, email, conversation_id)

def get_conversation_count(username: str, email: str) -> int:
    """
    Get the number of conversations for a user.

    Args:
        username (str): The username of the user
        email (str): The email of the user

    Returns:
        int: The number of conversations
    """
    logger.debug(f"Getting conversation count for {username} ({email})")
    return get_history_conversation_count(username, email)


# --- New Function ---
import json # Add json import

def get_conversation_history_as_input_list(username: str, email: str, conversation_id: str) -> List[Dict[str, str]]:
    """
    Fetches conversation history and formats it as a list of {'role': ..., 'content': ...}
    dictionaries suitable for AI agent input. Prioritizes structured messages from logs
    for assistant replies.

    Args:
        username (str): The username of the user.
        email (str): The email of the user.
        conversation_id (str): The ID of the conversation.

    Returns:
        List[Dict[str, str]]: Formatted history list for AI input.
    """
    logger.info(f"获取对话历史作为输入列表: User={username}, Email={email}, ConvoID={conversation_id}")
    history_for_agent = []
    try:
        # 1. Load raw messages from the repository
        history_messages = load_conversation(username=username, email=email, conversation_id=conversation_id)
        if not history_messages:
            logger.info(f"未找到对话历史: ConvoID={conversation_id}")
            return []

        logger.info(f"获取到 {len(history_messages)} 条原始历史消息，开始格式化...")

        # 2. Process messages, prioritizing logs for assistant
        processed_count = 0
        for msg in history_messages:
            role = msg.get('role')
            if role not in ['user', 'assistant']:
                continue # Skip non-user/assistant messages

            history_item = None
            # Try parsing output_as_input for assistant messages
            if role == 'assistant':
                output_content = msg.get('output_as_input') # Use the new field
                if output_content:
                    try:
                        parsed_output = json.loads(output_content)
                        # Check if it's the expected structured message format
                        if isinstance(parsed_output, dict) and 'role' in parsed_output and 'content' in parsed_output and parsed_output['role'] == 'assistant':
                            history_item = parsed_output # Use the structured message directly
                            logger.debug(f"使用 output_as_input 中的结构化消息 (ID: {msg.get('id', 'N/A')})")
                        else:
                            logger.debug(f"output_as_input 不是预期的结构化消息格式或角色不匹配 (ID: {msg.get('id', 'N/A')}), 回退到 content。")
                    except json.JSONDecodeError:
                        logger.debug(f"output_as_input 不是有效的 JSON (ID: {msg.get('id', 'N/A')}), 回退到 content。")
                    except Exception as parse_err:
                        logger.error(f"解析 output_as_input 时发生意外错误 (ID: {msg.get('id', 'N/A')}): {parse_err}", exc_info=True)
                        # Fallback on unexpected error

            # Fallback to using content if output_as_input wasn't used or role is 'user'
            if history_item is None:
                content = msg.get('content', '') # Ensure content is a string

                if role == 'user':
                    logger.info(f"处理用户历史消息 (ID: {msg.get('id', 'N/A')})")

                history_item = {"role": role, "content": content}
                logger.info(f"使用 content 作为历史消息 (ID: {msg.get('id', 'N/A')}, Role: {role})")

            if history_item.get("content") is None:
                 logger.warning(f"历史项 content 为 None (ID: {msg.get('id', 'N/A')}), 将设置为空字符串。")
                 history_item["content"] = ""


            history_for_agent.append(history_item)
            processed_count += 1

        logger.info(f"成功格式化 {processed_count} 条历史消息。")

        # 确保历史记录以助手消息结束，移除末尾的用户消息，并更新计数
        while history_for_agent and history_for_agent[-1].get("role") == "user":
            removed_msg = history_for_agent.pop()
            logger.info(f"移除了末尾的用户消息: {removed_msg}")
            processed_count -= 1 # 更新计数

        # 如果长度超过了MAX_HISTORY_MESSAGE_TO_INCLUDE，则截断到MAX_HISTORY_MESSAGE_TO_INCLUDE
        # 且必须要保证第一条消息的role=user
        if processed_count > MAX_HISTORY_MESSAGE_TO_INCLUDE:
            logger.info(f"历史消息数量超过限制 ({processed_count} > {MAX_HISTORY_MESSAGE_TO_INCLUDE})，进行截断处理。")
            history_for_agent = history_for_agent[-MAX_HISTORY_MESSAGE_TO_INCLUDE:]
            while history_for_agent[0].get("role") != "user":
                history_for_agent.pop(0)
                logger.info(f"移除了开头的非用户消息: {history_for_agent[0]}")
            
            processed_count = len(history_for_agent)
            
        logger.info(f"最终返回 {processed_count} 条历史消息给 AI。")
        return history_for_agent

    except Exception as e:
        logger.error(f"获取或格式化对话历史时出错 (ConvoID={conversation_id}): {e}", exc_info=True)
        return [] # Return empty list on error


def create_new_conversation_id() -> str:
    """
    Create a new conversation ID.

    Returns:
        str: A new conversation ID
    """
    return str(uuid.uuid4())


def check_conversation_ownership(username: str, email: str, conversation_id: str) -> tuple[bool, bool]:
    """
    检查用户是否是对话的所有者

    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID

    Returns:
        bool: 如果用户是对话的所有者，返回True，否则返回False
        bool: 如果对话存在，返回true，否则返回False
    """
    # 调用 repository 层的方法获取对话所有者信息
    owner_info = check_conversation_owner(conversation_id)

    if not owner_info:
        logger.warning(f"对话 {conversation_id} 不存在")
        return False, False

    owner_username = owner_info.get('username')
    owner_email = owner_info.get('email')

    # 检查用户名和邮箱是否匹配
    is_owner = (username == owner_username and email == owner_email)

    if not is_owner:
        logger.warning(f"用户 {username} ({email}) 不是对话 {conversation_id} 的所有者，所有者是 {owner_username} ({owner_email})")

    return is_owner, True


def get_user_latest_queries(user_email: str, limit: int = 10) -> List[str]:
    """
    获取指定用户最近的查询消息内容列表。

    Args:
        user_email (str): 用户邮箱
        limit (int): 最大返回数量，默认10

    Returns:
        List[str]: 用户最近的查询消息内容列表
    """
    logger.info(f"获取用户最新查询: {user_email}, limit={limit}")
    raw_queries = get_user_latest_queries_from_db(user_email, limit)

    # 过滤空查询，保留有效的文本内容
    filtered_queries = []
    for query in raw_queries:
        if query and query.strip():  # 只添加非空的文本内容
            filtered_queries.append(query)

    logger.info(f"为用户 {user_email} 获取到 {len(filtered_queries)} 条查询记录")
    return filtered_queries


def get_other_users_latest_queries(current_user_email: str, limit: int = 10) -> List[str]:
    """
    获取除当前用户外其他用户最近的查询消息内容列表。

    Args:
        current_user_email (str): 当前用户邮箱（将被排除）
        limit (int): 最大返回数量，默认10

    Returns:
        List[str]: 其他用户最近的查询消息内容列表
    """
    logger.info(f"获取其他用户最新查询: 排除用户 {current_user_email}, limit={limit}")
    raw_queries = get_other_users_latest_queries_from_db(current_user_email, limit)

    # 过滤空查询，保留有效的文本内容
    filtered_queries = []
    for query in raw_queries:
        if query and query.strip():  # 只添加非空的文本内容
            filtered_queries.append(query)

    logger.info(f"为其他用户获取到 {len(filtered_queries)} 条查询记录")
    return filtered_queries


def create_streaming_assistant_message_record(username: str, email: str, conversation_id: str,
                                            timestamp: Optional[int] = None, agent: Optional[str] = None) -> Optional[int]:
    """
    创建一条流式assistant消息记录，用于实时更新AI响应内容
    
    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID
        timestamp (int, optional): 时间戳，如果为None则使用当前时间
        agent (str, optional): agent名称
        
    Returns:
        Optional[int]: 创建的消息记录ID，失败时返回None
    """
    logger.info(f"创建流式assistant消息记录: {username} ({email}) in conversation {conversation_id}")
    return create_streaming_assistant_message(username, email, conversation_id, timestamp, agent)


def update_streaming_assistant_message_content(message_id: int, content: str, logs: Optional[str] = None,
                                             output_as_input: Optional[str] = None, agent: Optional[str] = None,
                                             time_spend: Optional[int] = None, is_completed: bool = False) -> bool:
    """
    更新流式assistant消息的内容和状态
    
    Args:
        message_id (int): 消息记录ID
        content (str): 更新的消息内容
        logs (str, optional): 执行日志
        output_as_input (str, optional): 结构化输出
        agent (str, optional): agent名称
        time_spend (int, optional): 响应耗时(秒)
        is_completed (bool): 是否已完成，True表示完成，False表示仍在处理中
        
    Returns:
        bool: 更新成功返回True，失败返回False
    """
    logger.debug(f"更新流式assistant消息: ID={message_id}, 内容长度={len(content)}, 是否完成={is_completed}")

    return update_streaming_assistant_message(
        message_id=message_id,
        content=content,
        logs=logs,
        output_as_input=output_as_input,
        agent=agent,
        time_spend=time_spend,
        is_completed=is_completed
    )


def get_current_streaming_message_id(username: str, email: str, conversation_id: str) -> Optional[int]:
    """
    获取当前正在处理中的流式assistant消息ID
    
    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID
        
    Returns:
        Optional[int]: 消息ID，如果没有找到则返回None
    """
    logger.debug(f"获取当前流式消息ID: {username} ({email}) in conversation {conversation_id}")
    return get_streaming_assistant_message_id(username, email, conversation_id)
